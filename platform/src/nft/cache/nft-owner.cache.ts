import { Inject, Singleton } from '../../core/ioc';
import { <PERSON>acheKeyGenerator } from '../../core/cache';
import { JsonSerialiser, Redis } from '../../core';
import { BlockchainNetwork } from '../../blockchain';
import { NFT } from '../nft';
import moment from 'moment';

@Singleton
export class NFTOwnerCache {
    private readonly CACHE_EXPIRY_MINS = 60;
    private readonly cacheKeyGenerator = new CacheKeyGenerator('NFT:Owner');

    constructor(
        @Inject private readonly redis: Redis,
        @Inject private readonly serialiser: JsonSerialiser) {
    }

    public async getAll(owner: string, address: string, network: BlockchainNetwork): Promise<NFT[]> {
        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const rawItems = await this.redis.cluster.hvals(cacheKey);

        if (!rawItems || rawItems.length === 0)
            return [];

        return rawItems.map(v => this.serialiser.deserialise<NFT>(v));
    }

    public async get(owner: string, address: string, network: BlockchainNetwork, nftId: number): Promise<NFT | undefined> {
        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const rawItem = await this.redis.cluster.hget(cacheKey, nftId.toString());

        if (!rawItem)
            return undefined;

        return this.serialiser.deserialise<NFT>(rawItem);
    }

    public async store(owner: string, address: string, network: BlockchainNetwork, ...nfts: NFT[]): Promise<void> {
        const validNfts = nfts.filter(nft => nft.address === address && nft.network === network && nft.owner === owner);

        if (validNfts.length === 0)
            return;

        const cacheKey = this.cacheKeyGenerator.generate(owner, address, network);
        const expireAt = moment()
            .add(this.CACHE_EXPIRY_MINS, 'minutes')
            .unix();

        const pipeline = this.redis.cluster.pipeline();

        for (const nft of validNfts)
            pipeline.hset(cacheKey, nft.id.toString(), this.serialiser.serialise(nft));

        pipeline.expireat(cacheKey, expireAt);

        await pipeline.exec();
    }

    public async remove(owner: string, nft: NFT): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(owner, nft.address, nft.network);

        await this.redis.cluster
            .pipeline()
            .hdel(cacheKey, nft.id.toString())
            .exec();
    }
}