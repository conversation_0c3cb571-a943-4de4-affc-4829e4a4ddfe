import { <PERSON>ton, Inject } from '../core/ioc';
import { LogClass } from '../core/logging';
import { NewNFT, NFT, NFTUpdate } from './nft';
import { NFTRepository } from './repositories/nft.repository';
import { NotFoundError, PagedResult } from '../core';
import { PlatformEventDispatcher } from '../core/events';
import { NFTOwnerChangedEvent, NFTAddedEvent, NFTOwnerTagsChangedEvent } from './events';
import { NFTFilter, NFTUserOwnedFilter } from './nft-filter';
import { BlockchainNetwork } from '../blockchain';
import { NFTOwner } from './nft-owner';
import { NFTOwnerCache } from './cache';
import _ from 'lodash';

@Singleton
@LogClass()
export class NFTManager {
    constructor(
        @Inject private readonly repository: NFTRepository,
        @Inject private readonly eventDispatcher: PlatformEventDispatcher,
        @Inject private readonly nftOwnerCache: NFTOwnerCache) {
    }

    public async getAll(filter?: NFTFilter): Promise<PagedResult<NFT>> {
        if (filter?.address)
            filter.address = filter.address.toLowerCase();

        return this.repository.getAll(filter);
    }

    public async getUserOwned(filter?: NFTUserOwnedFilter): Promise<PagedResult<NFT>> {
        return this.repository.getUserOwned(filter);
    }

    public async getUserOwners(collectionId: number): Promise<string[]> {
        return this.repository.getUserOwners(collectionId);
    }

    public async get(id: number): Promise<NFT | undefined> {
        return this.repository.getById(id);
    }

    public async getOwnersForTokens(address: string, network: BlockchainNetwork, tokenIds: string[]): Promise<NFTOwner[]>;
    public async getOwnersForTokens(collectionId: number, tokenIds: string[]): Promise<NFTOwner[]>;
    public async getOwnersForTokens(addressOrCollectionId: string | number, networkOrTokenIds: BlockchainNetwork | string[], tokenIds?: string[]): Promise<NFTOwner[]> {
        tokenIds = tokenIds || networkOrTokenIds as string[];

        if (typeof addressOrCollectionId === 'string') {
            if (!tokenIds?.length)
                throw new Error('Token IDs are required.');

            const address = addressOrCollectionId.toLowerCase();
            const network = networkOrTokenIds as BlockchainNetwork;
            return this.repository.getTokensOwners(address, network, tokenIds);
        } else {
            const collectionId = addressOrCollectionId;
            return this.repository.getTokensOwners(collectionId, tokenIds);
        }
    }

    public async getByAddress(address: string, network: BlockchainNetwork, tokenId: string): Promise<NFT | undefined> {
        address = address.toLowerCase();
        return this.repository.get(address, network, tokenId);
    }

    public async add(nft: NewNFT): Promise<NFT> {
        nft.owner = nft.owner?.toLowerCase();
        const added = await this.repository.add(nft);

        if (added.owner)
            await this.nftOwnerCache.store(added.owner, added.address, added.network, added);

        await this.eventDispatcher.send(
            new NFTAddedEvent(added),
            new NFTOwnerChangedEvent(added, undefined, added.owner)
        );

        return added;
    }

    public async update(nft: NFT, update: NFTUpdate): Promise<NFT>;
    public async update(id: number, update: NFTUpdate): Promise<NFT>;
    public async update(nftOrId: NFT | number, update: NFTUpdate): Promise<NFT> {
        const current = typeof nftOrId === 'number' ? await this.get(nftOrId) : nftOrId;

        if (!current)
            throw new NotFoundError('NFT not found.');

        update.owner = update.owner?.toLowerCase();
        const updated = await this.repository.update(current.id, update);

        if (!current)
            throw new NotFoundError('NFT not found.');

        if (current.owner)
            await this.nftOwnerCache.remove(current.owner, current);

        if (update.owner)
            await this.nftOwnerCache.store(update.owner, updated.address, updated.network, updated);

        if (current.owner?.toLowerCase() !== updated.owner?.toLowerCase())
            await this.eventDispatcher.send(new NFTOwnerChangedEvent(updated, current.owner, updated.owner));
        else if (!_.isEqual(_.sortBy(current.ownerTags), _.sortBy(updated.ownerTags)))
            await this.eventDispatcher.send(new NFTOwnerTagsChangedEvent(updated, current.ownerTags, updated.ownerTags));

        return updated;
    }

    public async setOwner(id: number, owner: string): Promise<void> {
        owner = owner.toLowerCase();
        const nft = await this.get(id);

        if (!nft)
            throw new NotFoundError('NFT not found.');

        if (nft.owner === owner) {
            await this.nftOwnerCache.store(owner, nft.address, nft.network, nft);
            return;
        }

        const from = nft.owner || undefined;
        nft.owner = owner;

        if (from)
            await this.nftOwnerCache.remove(from, nft);

        await this.repository.setOwner(id, owner);
        await this.nftOwnerCache.store(owner, nft.address, nft.network, nft);
        await this.eventDispatcher.send(new NFTOwnerChangedEvent(nft, from, owner));
    }

    public async setOwnerByAddress(address: string, network: BlockchainNetwork, tokenId: string, owner: string): Promise<void> {
        address = address.toLowerCase();
        owner = owner.toLowerCase();
        const nft = await this.getByAddress(address, network, tokenId);

        if (!nft)
            throw new NotFoundError('NFT not found.');

        if (nft.owner === owner) {
            await this.nftOwnerCache.store(owner, nft.address, nft.network, nft);
            return;
        }

        const from = nft.owner || undefined;
        nft.owner = owner;

        if (from)
            await this.nftOwnerCache.remove(from, nft);

        await this.repository.setOwner(nft.id, owner);
        await this.nftOwnerCache.store(owner, nft.address, nft.network, nft);
        await this.eventDispatcher.send(new NFTOwnerChangedEvent(nft, from, owner));
    }

    public async getOwnerTokens(owner: string, address: string, network: BlockchainNetwork): Promise<NFT[]> {
        const cached = await this.nftOwnerCache.getAll(owner, address, network);

        if (cached.length > 0)
            return cached;

        const nfts = await this.repository.getOwnerTokens(owner, address, network);

        if (nfts.length > 0)
            await this.nftOwnerCache.store(owner, address, network, ...nfts);

        return nfts;
    }
}