import { Inject, Singleton } from '../core/ioc';
import { LogClass, LogLevel } from '../core/logging';
import { NotFoundError } from '../core/errors';
import { <PERSON><PERSON>, JsonSerialiser } from '../core';
import { CacheKeyGenerator } from '../core/cache';
import { UserManager, UserAliasType } from '../user';
import { NFTManager } from './nft-manager';
import { BlockchainNetwork } from '../blockchain';
import { NFT } from './nft';

export interface NFTOwnershipRequirement {
    contractAddress: string;
    network: BlockchainNetwork;
}

export interface NFTOwnershipResult {
    requirement: NFTOwnershipRequirement;
    passed: boolean;
}

@Singleton
@LogClass({ level: LogLevel.Info })
export class NFTOwnershipChecker {
    private readonly cacheKeyGenerator = new CacheKeyGenerator('NFT:Ownership:Check');

    constructor(
        @Inject private readonly userManager: User<PERSON>anager,
        @Inject private readonly nftManager: NFTManager,
        @Inject private readonly redis: Redis,
        @Inject private readonly serialiser: JsonSerialiser) {
    }

    public async checkAll(userId: number, ...requirements: NFTOwnershipRequirement[]): Promise<boolean> {
        if (!requirements.length)
            return true;

        const results = await this.check(userId, ...requirements);
        return results.every(r => r.passed);
    }

    public async checkAny(userId: number, ...requirements: NFTOwnershipRequirement[]): Promise<boolean> {
        if (!requirements.length)
            return true;

        const results = await this.check(userId, ...requirements);
        return results.some(r => r.passed);
    }

    private async check(userId: number, ...requirements: NFTOwnershipRequirement[]): Promise<NFTOwnershipResult[]> {
        const user = await this.userManager.get(userId);

        if (!user)
            throw new NotFoundError(`User ${userId} not found.`);

        const ethereumAddress = user.aliases.find(a => a.type === UserAliasType.EthereumAddress)?.value;

        if (!ethereumAddress)
            throw new NotFoundError(`Alias for user ${userId} not found.`);

        let results: NFTOwnershipResult[] = [];

        for (const requirement of requirements) {
            const ownedNfts = await this.getOwnerTokens(ethereumAddress, requirement);

            results.push({
                requirement: requirement,
                passed: ownedNfts.length > 0
            });
        }

        return results;
    }

    private async getOwnerTokens(address: string, requirement: NFTOwnershipRequirement): Promise<NFT[]> {
        const cacheKey = this.cacheKeyGenerator.generate(address, requirement.contractAddress, requirement.network);
        const cached = await this.redis.cluster.get(cacheKey);

        if (cached)
            return this.serialiser.deserialise<NFT[]>(cached);

        // Add small 30-second cache to prevent repeated database hits when the user doesn't own any NFTs from the supplied collection.
        const ownedNfts = await this.nftManager.getOwnerTokens(address, requirement.contractAddress, requirement.network);
        await this.redis.cluster.setex(cacheKey, 30, this.serialiser.serialise(ownedNfts));
        return ownedNfts;
    }
}