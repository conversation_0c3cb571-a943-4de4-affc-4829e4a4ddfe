import { Inject, Singleton } from '../../core/ioc';
import Logger, { LogClass } from '../../core/logging';
import { ActiveSweepstake } from '../sweepstake-active';
import { NFTOwnershipChecker, NFTOwnershipRequirement } from '../../nft';

export interface SweepstakeGameBuyInNFTRequirementResult {
    requirements: NFTOwnershipRequirement[];
    passed: boolean;
}

@Singleton
@LogClass()
export class SweepstakeGameBuyInNFTRequirementProcessor {
    constructor(
        @Inject private readonly nftOwnershipChecker: NFTOwnershipChecker) {
    }

    public async process(sweepstake: ActiveSweepstake, userId: number): Promise<SweepstakeGameBuyInNFTRequirementResult> {
        if (!sweepstake.metadata?.nftOwnershipRequirement)
            return { requirements: [], passed: true };

        const requirements = this.extractNFTRequirements(sweepstake);

        if (requirements.length === 0)
            return { requirements: [], passed: true };

        Logger.debug('Checking if user owns NFT from required collection(s)...', {
            userId,
            sweepstakeId: sweepstake.id,
            requirements
        });

        const match = sweepstake.metadata?.nftOwnershipRequirementMatchType || 'ANY';
        const passed = match.toUpperCase() === 'ALL'
            ? await this.nftOwnershipChecker.checkAll(userId, ...requirements)
            : await this.nftOwnershipChecker.checkAny(userId, ...requirements);

        return { requirements, passed };
    }

    private extractNFTRequirements(sweepstake: ActiveSweepstake): NFTOwnershipRequirement[] {
        const nftOwnershipRequirements: NFTOwnershipRequirement[] = [];
        const requirements = sweepstake.metadata?.nftOwnershipRequirement;

        if (!Array.isArray(requirements) || requirements.length === 0)
            return nftOwnershipRequirements;

        for (const req of requirements)
            nftOwnershipRequirements.push({
                contractAddress: req.contractAddress,
                network: req.network
            });

        return nftOwnershipRequirements;
    }
}