import { describe, it, beforeEach } from '@tcom/test';
import { expect } from '@tcom/test/assert';
import { anything, instance, mock, reset, verify, when } from '@tcom/test/mock';
import { SweepstakeGameBuyInNFTRequirementProcessor } from '../../../src/sweepstake/processors';
import { NFTOwnershipChecker } from '../../../src/nft';
import { BlockchainNetwork } from '../../../src/blockchain';

describe('SweepstakeGameBuyInNFTRequirementProcessor', () => {
    const mockNFTOwnershipChecker = mock(NFTOwnershipChecker);

    function getProcessor(): SweepstakeGameBuyInNFTRequirementProcessor {
        return new SweepstakeGameBuyInNFTRequirementProcessor(
            instance(mockNFTOwnershipChecker)
        );
    }

    beforeEach(() => {
        reset(mockNFTOwnershipChecker);
    });

    describe('process()', () => {
        it('should return true when no NFT requirements exist', async () => {
            // Given
            const sweepstake = {
                id: 1,
                metadata: {}
            } as any;

            const userId = 123;
            const processor = getProcessor();

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.true;
            expect(result.requirements).to.deep.equal([]);
            verify(mockNFTOwnershipChecker.checkAny(anything(), anything())).never();
            verify(mockNFTOwnershipChecker.checkAll(anything(), anything())).never();
        });

        it('should return true when NFT requirements array is empty', async () => {
            // Given
            const sweepstake = {
                id: 1,
                metadata: {
                    nftOwnershipRequirement: []
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.true;
            expect(result.requirements).to.deep.equal([]);
            verify(mockNFTOwnershipChecker.checkAny(anything(), anything())).never();
            verify(mockNFTOwnershipChecker.checkAll(anything(), anything())).never();
        });

        it('should return true when user owns at least one NFT from required collections (ANY match)', async () => {
            // Given
            const nftRequirements = [
                {
                    contractAddress: '0x123456789abcdef',
                    network: BlockchainNetwork.Ethereum
                },
                {
                    contractAddress: '0xabcdef123456789',
                    network: BlockchainNetwork.Polygon
                }
            ];

            const sweepstake = {
                id: 1,
                metadata: {
                    nftOwnershipRequirement: nftRequirements,
                    nftOwnershipRequirementMatchType: 'ANY'
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            when(mockNFTOwnershipChecker.checkAny(userId, anything(), anything())).thenResolve(true);

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.true;
            expect(result.requirements).to.have.length(2);
            verify(mockNFTOwnershipChecker.checkAny(userId, anything(), anything())).once();
        });

        it('should return false when user does not own any NFTs from required collections', async () => {
            // Given
            const nftRequirements = [
                {
                    contractAddress: '0x123456789abcdef',
                    network: BlockchainNetwork.Ethereum
                },
                {
                    contractAddress: '0xabcdef123456789',
                    network: BlockchainNetwork.Polygon
                }
            ];

            const sweepstake = {
                id: 1,
                metadata: {
                    nftOwnershipRequirement: nftRequirements
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            when(mockNFTOwnershipChecker.checkAny(userId, anything(), anything())).thenResolve(false);

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.false;
            expect(result.requirements).to.have.length(2);
            verify(mockNFTOwnershipChecker.checkAny(userId, anything(), anything())).once();
        });

        it('should return true when user owns all NFTs from required collections (ALL match)', async () => {
            // Given
            const nftRequirements = [
                {
                    contractAddress: '0x123456789abcdef',
                    network: BlockchainNetwork.Ethereum
                },
                {
                    contractAddress: '0xabcdef123456789',
                    network: BlockchainNetwork.Polygon
                }
            ];

            const sweepstake = {
                id: 1,
                metadata: {
                    nftOwnershipRequirement: nftRequirements,
                    nftOwnershipRequirementMatchType: 'ALL'
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            when(mockNFTOwnershipChecker.checkAll(userId, anything(), anything())).thenResolve(true);

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.true;
            expect(result.requirements).to.have.length(2);
            verify(mockNFTOwnershipChecker.checkAll(userId, anything(), anything())).once();
        });

        it('should return false when user does not own all NFTs from required collections (ALL match)', async () => {
            // Given
            const nftRequirements = [
                {
                    contractAddress: '0x123456789abcdef',
                    network: BlockchainNetwork.Ethereum
                },
                {
                    contractAddress: '0xabcdef123456789',
                    network: BlockchainNetwork.Polygon
                }
            ];

            const sweepstake = {
                id: 1,
                metadata: {
                    nftOwnershipRequirement: nftRequirements,
                    nftOwnershipRequirementMatchType: 'ALL'
                }
            } as any;

            const userId = 123;
            const processor = getProcessor();

            when(mockNFTOwnershipChecker.checkAll(userId, anything(), anything())).thenResolve(false);

            // When
            const result = await processor.process(sweepstake, userId);

            // Then
            expect(result.passed).to.be.false;
            expect(result.requirements).to.have.length(2);
            verify(mockNFTOwnershipChecker.checkAll(userId, anything(), anything())).once();
        });
    });
});
